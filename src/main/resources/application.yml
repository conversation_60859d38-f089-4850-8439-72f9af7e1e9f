# Spring Boot Configuration for DDD Aviation Pricing System
# This configuration sets up an in-memory H2 database for learning purposes

spring:
  # Application configuration
  application:
    name: ddd-aviation-pricing-system
  
  # Database configuration - H2 in-memory database
  datasource:
    url: jdbc:h2:mem:aviation_db;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # H2 Console configuration (for development and learning)
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        web-allow-others: false
  
  # JPA/Hibernate configuration
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop  # Recreate schema on startup (good for learning)
    show-sql: true  # Show SQL queries in logs (good for learning)
    properties:
      hibernate:
        format_sql: true  # Format SQL queries for better readability
        use_sql_comments: true  # Add comments to generated SQL
        jdbc:
          batch_size: 20  # Batch size for better performance
        order_inserts: true
        order_updates: true
  
  # Jackson JSON configuration
  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
    default-property-inclusion: NON_NULL

# Logging configuration
logging:
  level:
    com.example.ddddemo: DEBUG  # Debug level for our application
    org.hibernate.SQL: DEBUG   # Show SQL statements
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE  # Show SQL parameters
    org.springframework.transaction: DEBUG  # Show transaction boundaries
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"

# Management endpoints (Spring Boot Actuator)
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env
  endpoint:
    health:
      show-details: always

# Custom application properties for our DDD system
aviation:
  pricing:
    # Default pricing configuration
    base-price: 100.00
    currency: USD
    # Pricing factors
    advance-booking-discount-days: 30
    advance-booking-discount-percentage: 15
    weekend-surcharge-percentage: 20
    popular-route-surcharge-percentage: 25
    loyalty-discount-percentage: 10
  
  # Business rules configuration
  business-rules:
    max-passengers-per-booking: 9
    booking-deadline-hours: 2  # Hours before departure when booking closes
    cancellation-deadline-hours: 24  # Hours before departure for free cancellation
